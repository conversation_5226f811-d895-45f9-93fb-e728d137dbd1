import { useGSAP } from '@gsap/react';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import gsap from 'gsap';
import { useRef } from 'react';

gsap.registerPlugin(ScrollTrigger);

export default function App() {
  const boxRef = useRef();

  useGSAP(() => {
    gsap.from(boxRef.current, {
      y: 750,
      duration: 1,
      ease: 'power2.out',
      borderRadius: '100%',
      ro
      scrollTrigger: {
        trigger: boxRef.current,
        start: 'bottom 70%',
        // end: 'top 30%',
        scrub: true, // ties animation progress to scroll
      },
    });
  }, []);

  return (
    <div className="w-full h-[150vh] ">
      <h1 className="text-white text-5xl font-bold mb-24 tracking-wider uppercase">
        GSAP ScrollTrigger Showcase
      </h1>

      <div className="flex items-center justify-center gap-16 mt-20">
        <div
          ref={boxRef}
          className="w-40 h-40  bg-gradient-to-tr from-green-400 to-green-600 shadow-lg shadow-green-500/50 border-4 border-green-600"
        ></div>
      </div>
    </div>
  );
}
